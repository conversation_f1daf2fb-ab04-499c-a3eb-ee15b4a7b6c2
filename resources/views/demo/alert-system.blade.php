@extends('layouts.default')

@section('title', 'Demo Alert System')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Demo Alert System</h1>
        
        <!-- Global Alert Demo -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Global Alert System</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="GlobalAlert.success('Thành công! Thao tác đã được thực hiện.')" 
                        class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Success Alert
                </button>
                <button onclick="GlobalAlert.error('Lỗi! <PERSON><PERSON> sự cố xảy ra.')" 
                        class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    <PERSON>rro<PERSON>
                </button>
                <button onclick="GlobalAlert.warning('Cảnh báo! Vui lòng kiểm tra lại.')" 
                        class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    Warning Alert
                </button>
                <button onclick="GlobalAlert.info('Thông tin: Đây là thông báo thông tin.')" 
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Info Alert
                </button>
            </div>
            
            <div class="mt-4">
                <button onclick="showCustomAlert()" 
                        class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Custom Alert with Options
                </button>
                <button onclick="GlobalAlert.clearAll()" 
                        class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 ml-2">
                    Clear All Alerts
                </button>
            </div>
        </div>

        <!-- Email Verification Demo -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Email Verification Status</h2>
            
            <h3 class="text-lg font-medium mb-2">Inline Display:</h3>
            <x-notification.email-verification-status 
                :user="auth('customer_user')->user()" 
                type="user" 
                :compact="false" />
            
            <h3 class="text-lg font-medium mb-2 mt-6">Compact Display:</h3>
            <x-notification.email-verification-status 
                :user="auth('customer_user')->user()" 
                type="user" 
                :compact="true" />
            
            <div class="mt-4">
                <button onclick="showEmailVerificationAlert()" 
                        class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                    Show as Global Alert
                </button>
            </div>
        </div>

        <!-- Contact Modal Demo -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Contact Modal</h2>
            <p class="text-gray-600 mb-4">
                @auth('customer_user')
                    Bạn đã đăng nhập. Khi mở modal, các trường sẽ được tự động điền và không thể chỉnh sửa.
                @else
                    Bạn chưa đăng nhập. Các trường trong modal sẽ có thể chỉnh sửa bình thường.
                @endauth
            </p>
            <button onclick="$store.modal.open()" 
                    class="bg-emerald-500 text-white px-4 py-2 rounded hover:bg-emerald-600">
                Open Contact Modal
            </button>
        </div>

        <!-- Template Favorites Demo -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Template Favorites (Rate Limited)</h2>
            <p class="text-gray-600 mb-4">
                Thử nhấn nhanh nhiều lần để test rate limiting (10 lần/phút).
            </p>
            <div class="flex space-x-4">
                <button onclick="testFavoriteToggle(1)" 
                        class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    ❤️ Toggle Favorite Template 1
                </button>
                <button onclick="testFavoriteToggle(2)" 
                        class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    ❤️ Toggle Favorite Template 2
                </button>
            </div>
        </div>

        <!-- Advanced Filter Demo -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Advanced Template Filter</h2>
            <p class="text-gray-600 mb-4">
                Xem advanced filter system tại <a href="{{ route('templates.index') }}" class="text-emerald-600 underline">trang template</a>.
            </p>
            <div class="bg-gray-100 p-4 rounded">
                <h3 class="font-medium mb-2">Các tính năng filter:</h3>
                <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                    <li>Tìm kiếm theo tên và mô tả</li>
                    <li>Lọc theo danh mục</li>
                    <li>Lọc theo tính năng (multiple selection)</li>
                    <li>Lọc theo khoảng giá</li>
                    <li>Lọc template nổi bật</li>
                    <li>Lọc template đang giảm giá</li>
                    <li>Sắp xếp theo nhiều tiêu chí</li>
                    <li>Hiển thị active filters với khả năng xóa từng filter</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Include Contact Modal -->
<x-modal.contact-modal />

<script>
function showCustomAlert() {
    GlobalAlert.show(
        'Đây là alert tùy chỉnh với nhiều tùy chọn!', 
        'info', 
        {
            title: 'Alert Tùy Chỉnh',
            duration: 8000,
            position: 'top-left',
            showProgress: true
        }
    );
}

function showEmailVerificationAlert() {
    // Simulate showing email verification as alert
    const message = `
        <div class="space-y-2">
            <p class="font-medium">Email chưa được xác thực</p>
            <p class="text-xs opacity-90"><EMAIL> • Cần xác thực email</p>
            <div class="mt-2">
                <button onclick="GlobalAlert.success('Đã gửi email xác thực!')" 
                        class="text-xs font-medium underline hover:no-underline">
                    Gửi lại email xác thực
                </button>
            </div>
        </div>
    `;
    
    GlobalAlert.warning(message, {
        title: 'Cần xác thực email',
        position: 'top-center',
        autoHide: false,
        dismissible: true
    });
}

function testFavoriteToggle(templateId) {
    // Simulate template favorite toggle with rate limiting
    fetch(`/template-favorites/${templateId}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (response.status === 429) {
            return response.json().then(data => {
                throw new Error(data.message || 'Rate limited');
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            GlobalAlert.success(data.message || 'Đã cập nhật yêu thích!');
        } else {
            GlobalAlert.error(data.message || 'Có lỗi xảy ra');
        }
    })
    .catch(error => {
        GlobalAlert.error(error.message || 'Có lỗi xảy ra');
    });
}
</script>
@endsection
