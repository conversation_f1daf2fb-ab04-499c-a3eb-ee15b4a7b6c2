@props([
    'user' => null,
    'customer' => null,
    'type' => 'user' // 'user' for CustomerUser, 'company' for Customer
])

@php
    $entity = $type === 'company' ? $customer : $user;
    $isVerified = $entity && $entity->hasVerifiedEmail();
    $email = $entity ? $entity->email : null;

    // Determine route based on type
    $resendRoute = $type === 'company' ? 'customer.verification.send' : 'verification.send';
@endphp

@if($email)
    <div class="mb-4">
        @if($isVerified)
            <!-- Verified Status -->
            <div class="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">
                        {{ $type === 'company' ? __('messages.email.company_email_verified') : __('messages.email.user_email_verified') }}
                    </p>
                    <p class="text-xs text-green-600 mt-1">
                        {{ $email }} • {{ __('messages.email.verified_on') }} {{ $entity->email_verified_at->format('d/m/Y H:i') }}
                    </p>
                </div>
            </div>
        @else
            <!-- Unverified Status -->
            <div class="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium text-yellow-800">
                        {{ $type === 'company' ? __('messages.email.company_email_unverified') : __('messages.email.user_email_unverified') }}
                    </p>
                    <p class="text-xs text-yellow-600 mt-1">
                        {{ $email }} • {{ __('messages.email.verification_required') }}
                    </p>
                </div>
                <div class="ml-3">
                    <form method="POST" action="{{ route($resendRoute) }}" class="inline">
                        @csrf
                        @if($type === 'company')
                            <input type="hidden" name="email" value="{{ $email }}">
                        @endif
                        <button type="submit"
                                class="text-xs font-medium text-yellow-700 hover:text-yellow-800 underline focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 rounded">
                            {{ __('messages.email.resend_verification') }}
                        </button>
                    </form>
                </div>
            </div>
        @endif
    </div>
@endif
