<?php

use App\Http\Controllers\Auth\EmailVerificationController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\VerifyCustomerEmailController;
use App\Http\Controllers\ConsultationController;
use App\Http\Controllers\DemoController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\UserDashboardController;
use Illuminate\Support\Facades\Route;

// Language Switcher
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// Demo template routes (specific routes first)
Route::get('/xem-demo', [DemoController::class, 'index'])->name('demo.vi.index');
Route::get('/view-demo', [DemoController::class, 'index'])->name('demo.index');

// Demo template detail routes
Route::get('/xem-demo/{slug}', [DemoController::class, 'show'])->name('demo.vi.show');
Route::get('/view-demo/{slug}', [DemoController::class, 'show'])->name('demo.show');

// Template Gallery routes
Route::get('/mau-website', [TemplateController::class, 'index'])->name('templates.vi.index');
Route::get('/website-templates', [TemplateController::class, 'index'])->name('templates.index');

// Template category routes (more specific)
Route::get('/mau-website/danh-muc/{category_slug}', [TemplateController::class, 'index'])->name('templates.vi.category');
Route::get('/website-templates/category/{category_slug}', [TemplateController::class, 'index'])->name('templates.category');

// Template detail routes
Route::get('/mau-website/{slug}', [TemplateController::class, 'show'])->name('templates.vi.show');
Route::get('/website-templates/{slug}', [TemplateController::class, 'show'])->name('templates.show');

// Consultation routes
Route::get('/tu-van/{category_slug}', [ConsultationController::class, 'index'])->name('consultation.vi.index');
Route::get('/consultation/{category_slug}', [ConsultationController::class, 'index'])->name('consultation.index');

// Consultation detail routes
Route::get('/tu-van/{category_slug}/{detail_slug}', [ConsultationController::class, 'detail'])->name('consultation.vi.detail');
Route::get('/consultation/{category_slug}/{detail_slug}', [ConsultationController::class, 'detail'])->name('consultation.detail');


// ############################################
// ############ Email Verification ############
// ############################################

// Customer User - Email Verification Routes (accessible without authentication)
Route::get('/email/verify/{id}/{hash}', \App\Http\Controllers\Auth\VerifyEmailController::class)
    ->middleware(['signed', 'throttle:6,1'])
    ->name('verification.verify');

// Customer User - Resend Verification Email (accessible only to authenticated users)
Route::post('/email/verification-notification', [EmailVerificationController::class, 'resend'])
    ->middleware(['auth:customer_user', 'throttle:6,1'])
    ->name('verification.send');

// Customer - Email Verification Routes
Route::get('/customer/email/verify/{id}/{hash}/{token?}', [VerifyCustomerEmailController::class, '__invoke'])
    ->middleware(['signed', 'throttle:6,1'])
    ->name('customer.verification.verify');

// Customer - Resend Verification Email (accessible only to authenticated users)
Route::post('/customer/email/verification-notification', [VerifyCustomerEmailController::class, 'resend'])
    ->middleware('throttle:6,1')
    ->name('customer.verification.send');


// ############################################
// ############## Authentication ##############
// ############################################
Route::middleware('guest:customer_user')->group(function () {
    // Login Routes
    Route::get('/dang-nhap', [LoginController::class, 'showLoginForm'])->name('login.vi');
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');

    Route::post('/dang-nhap', [LoginController::class, 'login'])->name('login.vi.post');
    Route::post('/login', [LoginController::class, 'login'])->name('login.post');

    // Registration Routes
    Route::get('/dang-ky', [\App\Http\Controllers\Auth\RegisterController::class, 'create'])->name('register.vi');
    Route::get('/sign-up', [\App\Http\Controllers\Auth\RegisterController::class, 'create'])->name('register');

    Route::post('/dang-ky', [\App\Http\Controllers\Auth\RegisterController::class, 'store']);
    Route::post('/sign-up', [\App\Http\Controllers\Auth\RegisterController::class, 'store']);

    // Password Reset Routes
    Route::get('/quen-mat-khau', [\App\Http\Controllers\Auth\ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.vi.request');
    Route::get('/forgot-password', [\App\Http\Controllers\Auth\ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/quen-mat-khau', [\App\Http\Controllers\Auth\ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.vi.email');
    Route::post('/forgot-password', [\App\Http\Controllers\Auth\ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');

    Route::get('/dat-lai-mat-khau/{token}', [\App\Http\Controllers\Auth\ResetPasswordController::class, 'showResetForm'])->name('password.vi.reset');
    Route::get('/reset-password/{token}', [\App\Http\Controllers\Auth\ResetPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('/dat-lai-mat-khau', [\App\Http\Controllers\Auth\ResetPasswordController::class, 'reset'])->name('password.vi.update');
    Route::post('/reset-password', [\App\Http\Controllers\Auth\ResetPasswordController::class, 'reset'])->name('password.update');
});

Route::middleware('auth:customer_user')->group(function () {
    Route::post('/dang-xuat', [LoginController::class, 'logout'])->name('logout.vi');
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    // User Profile
    Route::prefix('/dashboard')->group(function () {
        Route::post('/', [UserDashboardController::class, 'update'])->name('user-dashboard.update');
        Route::get('/', [UserDashboardController::class, 'index'])->name('user-dashboard');

        Route::post('/company', [UserDashboardController::class, 'updateCompany'])->name('user-dashboard.update-company');

        // Website Management Routes
        Route::resource('websites', \App\Http\Controllers\WebsiteController::class)->except(['index', 'create', 'edit']);
        Route::post('/websites/{website}/convert-to-trial', [\App\Http\Controllers\WebsiteController::class, 'convertToTrial'])->name('websites.convert-to-trial');
        Route::post('/websites/{website}/convert-to-production', [\App\Http\Controllers\WebsiteController::class, 'convertToProduction'])->name('websites.convert-to-production');

        // Template Favorites Routes
        Route::get('/favorites', [\App\Http\Controllers\TemplateFavoriteController::class, 'index'])->name('user-dashboard.favorites');
        Route::post('/template-favorites/{template}/toggle', [\App\Http\Controllers\TemplateFavoriteController::class, 'toggle'])
            ->middleware(\App\Http\Middleware\TemplateFavoriteRateLimit::class)
            ->name('template-favorites.toggle');
        Route::delete('/template-favorites/{template}', [\App\Http\Controllers\TemplateFavoriteController::class, 'destroy'])
            ->middleware(\App\Http\Middleware\TemplateFavoriteRateLimit::class)
            ->name('template-favorites.destroy');

        // ...
    });
});

// Demo routes (for development/testing/local)
if (app()->isLocal()) {
    Route::get('/demo/alert-system', function () {
        return view('demo.alert-system');
    })->name('demo.alert-system');
}

// Catch-all route for pages (MUST BE LAST)
Route::get('/{slug}', [PageController::class, 'showPage'])
    ->where('slug', '.*')
    ->name('pages.show');
